# Product Overview

**web2img** is a web application built with AdonisJS that appears to be designed for web-to-image conversion functionality.

## Key Features
- User authentication system with email/password login
- Token-based API authentication using access tokens
- RESTful API architecture
- Database-backed user management

## Current State
The application is in early development with basic user authentication infrastructure in place. The core web-to-image functionality appears to be planned but not yet implemented.

## Architecture
- Backend API built with AdonisJS framework
- MySQL database for data persistence
- Token-based authentication for API access
- Modular middleware-based request processing
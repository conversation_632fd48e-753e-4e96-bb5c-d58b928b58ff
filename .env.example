TZ=UTC
PORT=3333
HOST=localhost
LOG_LEVEL=info
APP_KEY=
NODE_ENV=development

# Database Configuration
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_DATABASE=app

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ImgProxy Configuration
IMGPROXY_URL=http://localhost:8080
IMGPROXY_KEY=
IMGPROXY_SALT=

# Screenshot Storage Configuration
STORAGE_PATH=storage/screenshots
STORAGE_BASE_URL=http://localhost:3333/storage

# Screenshot System Configuration
SCREENSHOT_TIMEOUT=30000
SCREENSHOT_CACHE_TTL=3600
SCREENSHOT_MAX_CONCURRENT=10
SCREENSHOT_QUEUE_CONCURRENCY=3

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000